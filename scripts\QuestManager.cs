using Godot;
using System.Collections.Generic;
using System.Linq;

public static class QuestManager
{
	private static readonly List<Quest> _allQuests = new List<Quest>
	{
		new Quest
		{
			Id = 1,
			Header = "QUEST_1_HEADER",
			Description = "QUEST_1_DESCRIPTION",
			GoldReward = 100,
			XpReward = 0,
			Requirements = new List<QuestRequirement>
			{
				new QuestRequirement { ResourceType = ResourceType.Wood, Amount = 1 },
			}
		},
		new Quest
		{
			Id = 2,
			Header = "QUEST_2_HEADER",
			Description = "QUEST_2_DESCRIPTION",
			GoldReward = 200,
			XpReward = 0,
			Requirements = new List<QuestRequirement>
			{
				new QuestRequirement { ResourceType = ResourceType.Plank, Amount = 5 },
				new QuestRequirement { ResourceType = ResourceType.WoodenStick, Amount = 3 }
			}
		},
		new Quest
		{
			Id = 3,
			Header = "QUEST_3_HEADER",
			Description = "QUEST_3_DESCRIPTION",
			GoldReward = 0,
			XpReward = 100,
			Requirements = new List<QuestRequirement>
			{
				new QuestRequirement { ResourceType = ResourceType.Berry, Amount = 15 }
			}
		}
	};

	public static List<Quest> GetAvailableQuests()
	{
		var questData = GameSaveData.Instance.QuestData;
		return _allQuests.Where(q => 
			questData.ActivatedQuests.Contains(q.Id) && 
			!questData.CompletedQuests.Contains(q.Id)
		).ToList();
	}

	public static Quest GetQuestById(int id)
	{
		return _allQuests.FirstOrDefault(q => q.Id == id);
	}

	public static bool CanCompleteQuest(int questId)
	{
		var quest = GetQuestById(questId);
		if (quest == null) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var requirement in quest.Requirements)
		{
			if (!resourcesManager.HasResource(requirement.ResourceType, requirement.Amount))
			{
				return false;
			}
		}

		return true;
	}

	public static bool CompleteQuest(int questId)
	{
		var quest = GetQuestById(questId);
		if (quest == null) return false;

		if (!CanCompleteQuest(questId)) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		var questData = GameSaveData.Instance.QuestData;
		if (questData.CompletedQuests.Contains(questId)) return false;

		foreach (var requirement in quest.Requirements)
		{
			if (!resourcesManager.RemoveResource(requirement.ResourceType, requirement.Amount))
			{
				GD.PrintErr($"QuestManager: Failed to remove {requirement.Amount} {requirement.ResourceType} for quest {questId}");
				return false;
			}
		}

		if (quest.GoldReward > 0)
		{
			GameSaveData.Instance.PlayerStats.Money += quest.GoldReward;
			resourcesManager.EmitStatChanged("Money", GameSaveData.Instance.PlayerStats.Money, GameSaveData.Instance.PlayerStats.Money - quest.GoldReward);
		}

		if (quest.XpReward > 0)
		{
			CommonSignals.Instance?.EmitAddXp(quest.XpReward);
		}

		questData.CompletedQuests.Add(questId);

		GD.Print($"QuestManager: Completed quest {questId} - Gold: {quest.GoldReward}, XP: {quest.XpReward}");
		return true;
	}

	public static void ActivateQuest(int questId)
	{
		var questData = GameSaveData.Instance.QuestData;
		if (!questData.ActivatedQuests.Contains(questId))
		{
			questData.ActivatedQuests.Add(questId);
			GD.Print($"QuestManager: Activated quest {questId}");
		}
	}

	public static void ActivateInitialQuests()
	{
		ActivateQuest(1);
		ActivateQuest(2);
		ActivateQuest(3);
	}
}
