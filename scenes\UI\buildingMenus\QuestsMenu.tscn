[gd_scene load_steps=38 format=3 uid="uid://bcswpp68n3vmf"]

[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="2_o8igg"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="3_lwf55"]
[ext_resource type="Texture2D" uid="uid://bvxwrqkd3vmkd" path="res://resources/solaria/UI/icon_chest.png" id="3_uc5in"]
[ext_resource type="Texture2D" uid="uid://rbmx7uwcpffw" path="res://resources/solaria/resources/resource_plank.png" id="4_kubw5"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="5_865ov"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="6_4rxqb"]
[ext_resource type="Texture2D" uid="uid://dulxk4qo8hng" path="res://resources/solaria/resources/coin_resource.png" id="7_o8igg"]
[ext_resource type="Texture2D" uid="uid://cmtjviq7kvftk" path="res://resources/solaria/UI/empty_button.png" id="7_pye2h"]
[ext_resource type="Texture2D" uid="uid://bdscl0odejahl" path="res://resources/solaria/resources/resource_stone.png" id="7_qabh4"]
[ext_resource type="Texture2D" uid="uid://beywyaey04k76" path="res://resources/solaria/UI/icon_yes.png" id="8_o8igg"]
[ext_resource type="Texture2D" uid="uid://d1o5k1oj0w0j7" path="res://resources/solaria/UI/icon_minus.png" id="10_lwf55"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="12_fdgcv"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wltr5"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bu41f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_titi1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_addpo"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cfprg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4bpbu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iq3py"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yklww"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_xf8fr"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_07hwm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_oxiyu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mes8s"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sapyx"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_b4v27"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jlemb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3sn0u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kauif"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jurd5"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jpeu1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_48vcc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wrunb"]

[node name="QuestsMenu" type="CanvasLayer"]

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_o8igg")

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListDark" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxFlat_bu41f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(17.4737, 23.579)
texture = ExtResource("3_lwf55")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(17.4737, 23.579)
scale = Vector2(1.4, 1.4)
texture = ExtResource("3_uc5in")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(141.32, 7.15791)
texture = ExtResource("5_865ov")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 119.0
offset_top = 1.0
offset_right = 138.0
offset_bottom = 18.0
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 2

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(141.32, 20.8421)
texture = ExtResource("7_qabh4")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 119.0
offset_top = 15.0
offset_right = 138.0
offset_bottom = 32.0
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 2

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(141.32, 33.4737)
texture = ExtResource("7_qabh4")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 119.0
offset_top = 28.0
offset_right = 138.0
offset_bottom = 45.0
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 2

[node name="RewardAmount" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 139.368
offset_top = 39.1579
offset_right = 184.368
offset_bottom = 56.1579
scale = Vector2(0.73, 0.73)
text = "20000"
horizontal_alignment = 2

[node name="RewardIcon" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(178.316, 45.4737)
texture = ExtResource("7_o8igg")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("7_pye2h")

[node name="CanClaimStatus" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
position = Vector2(165.684, 19.1579)
texture = ExtResource("8_o8igg")

[node name="Header" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 161.0
offset_bottom = 17.0
scale = Vector2(0.73, 0.73)
text = "QUEST_1_HEADER"
horizontal_alignment = 0

[node name="Description" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 217.0
offset_bottom = 87.0
scale = Vector2(0.495, 0.495)
text = "QUEST_1_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListDark"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="ItemListLight" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxEmpty_wrunb")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(17.4737, 22.2106)
texture = ExtResource("3_lwf55")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(17.4737, 22.2106)
scale = Vector2(1.4, 1.4)
texture = ExtResource("3_uc5in")

[node name="PriceIcon1" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(141.425, 6.84214)
texture = ExtResource("4_kubw5")

[node name="Price1" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 114.105
offset_top = 0.68421
offset_right = 140.105
offset_bottom = 17.6842
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 2

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(141.425, 19.4737)
texture = ExtResource("7_qabh4")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 114.105
offset_top = 13.6842
offset_right = 140.105
offset_bottom = 30.6842
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 2

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(141.425, 32.1053)
texture = ExtResource("7_qabh4")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 114.105
offset_top = 26.3158
offset_right = 140.105
offset_bottom = 43.3158
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 2

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("7_pye2h")

[node name="CanClaimStatus" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(165.684, 19.3684)
texture = ExtResource("10_lwf55")

[node name="Header" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 34.1053
offset_top = -1.15788
offset_right = 178.105
offset_bottom = 14.8421
scale = Vector2(0.73, 0.73)
text = "QUEST_2_HEADER"
horizontal_alignment = 0

[node name="Description" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight" instance=ExtResource("6_4rxqb")]
layout_mode = 0
offset_left = 34.0
offset_top = 11.0
offset_right = 198.0
offset_bottom = 90.0
scale = Vector2(0.495, 0.495)
text = "QUEST_2_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="RewardAmount" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight" instance=ExtResource("6_4rxqb")]
offset_left = 138.316
offset_top = 37.7895
offset_right = 181.316
offset_bottom = 54.7895
scale = Vector2(0.73, 0.73)
text = "20000"
horizontal_alignment = 2

[node name="RewardIcon" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListLight"]
position = Vector2(176.211, 44.1053)
texture = ExtResource("7_o8igg")

[node name="Close" type="Sprite2D" parent="Control/Panel"]
position = Vector2(101.263, -125.053)
texture = ExtResource("12_fdgcv")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 91.1053
offset_top = -137.474
offset_right = 111.105
offset_bottom = -115.474
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")
