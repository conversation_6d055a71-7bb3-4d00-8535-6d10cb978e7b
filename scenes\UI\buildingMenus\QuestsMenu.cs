using Godot;
using System.Collections.Generic;
using System.Linq;

public partial class QuestsMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private VBoxContainer _questContainer;

	[Export] public Texture2D GoldRewardTexture { get; set; }
	[Export] public Texture2D XpRewardTexture { get; set; }
	[Export] public Texture2D CanClaimTexture { get; set; }
	[Export] public Texture2D CantClaimTexture { get; set; }

	private List<QuestItemUI> _questItems = new List<QuestItemUI>();

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("QuestsMenu: AnimationPlayer not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("QuestsMenu: Close button not found!");
			return;
		}

		_questContainer = GetNode<VBoxContainer>("Control/Panel/ScrollContainer/VBoxContainer");
		if (_questContainer == null)
		{
			GD.PrintErr("QuestsMenu: VBoxContainer not found!");
			return;
		}

		_closeButton.Pressed += CloseMenu;

		// Initially hide the menu
		var panel = GetNode<Sprite2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}
	}

	public void OpenMenu()
	{
		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		RefreshQuestList();

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}
	}

	private void CloseMenu()
	{
		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	private void RefreshQuestList()
	{
		var availableQuests = QuestManager.GetAvailableQuests();
		
		// Clear existing quest items
		_questItems.Clear();

		// Get existing ItemList nodes (Dark and Light templates)
		var darkTemplate = _questContainer.GetNode<ItemList>("ItemListDark");
		var lightTemplate = _questContainer.GetNode<ItemList>("ItemListLight");

		// Hide both templates initially
		if (darkTemplate != null) darkTemplate.Visible = false;
		if (lightTemplate != null) lightTemplate.Visible = false;

		// Show and configure quest items based on available quests
		for (int i = 0; i < availableQuests.Count && i < 2; i++)
		{
			var quest = availableQuests[i];
			ItemList template = (i % 2 == 0) ? darkTemplate : lightTemplate;
			
			if (template != null)
			{
				template.Visible = true;
				SetupQuestItem(template, quest);
				_questItems.Add(new QuestItemUI { ItemList = template, Quest = quest });
			}
		}
	}

	private void SetupQuestItem(ItemList itemList, Quest quest)
	{
		// Set header and description
		var header = itemList.GetNode<Label>("Header");
		var description = itemList.GetNode<Label>("Description");
		
		if (header != null) header.Text = Tr(quest.Header);
		if (description != null) description.Text = Tr(quest.Description);

		// Set up requirements (Price1, Price2, Price3)
		SetupRequirement(itemList, "PriceIcon1", "Price1", quest.Requirements.ElementAtOrDefault(0));
		SetupRequirement(itemList, "PriceIcon2", "Price2", quest.Requirements.ElementAtOrDefault(1));
		SetupRequirement(itemList, "PriceIcon3", "Price3", quest.Requirements.ElementAtOrDefault(2));

		// Set up reward
		SetupReward(itemList, quest);

		// Set up can claim status
		SetupClaimStatus(itemList, quest);

		// Set up button
		var button = itemList.GetNode<Button>("Button");
		if (button != null)
		{
			// Disconnect any existing connections
			if (button.IsConnected(Button.SignalName.Pressed, Callable.From(() => OnQuestButtonPressed(quest.Id))))
			{
				button.Disconnect(Button.SignalName.Pressed, Callable.From(() => OnQuestButtonPressed(quest.Id)));
			}
			button.Pressed += () => OnQuestButtonPressed(quest.Id);
		}
	}

	private void SetupRequirement(ItemList itemList, string iconName, string labelName, QuestRequirement requirement)
	{
		var icon = itemList.GetNode<Sprite2D>(iconName);
		var label = itemList.GetNode<Label>(labelName);

		if (requirement != null)
		{
			if (icon != null)
			{
				icon.Visible = true;
				icon.Texture = TextureManager.Instance?.GetResourceTexture(requirement.ResourceType);
			}
			if (label != null)
			{
				label.Visible = true;
				label.Text = requirement.Amount.ToString();
			}
		}
		else
		{
			if (icon != null) icon.Visible = false;
			if (label != null) label.Visible = false;
		}
	}

	private void SetupReward(ItemList itemList, Quest quest)
	{
		var rewardIcon = itemList.GetNode<Sprite2D>("RewardIcon");
		var rewardAmount = itemList.GetNode<Label>("RewardAmount");

		if (quest.GoldReward > 0)
		{
			if (rewardIcon != null) rewardIcon.Texture = GoldRewardTexture;
			if (rewardAmount != null) rewardAmount.Text = quest.GoldReward.ToString();
		}
		else if (quest.XpReward > 0)
		{
			if (rewardIcon != null) rewardIcon.Texture = XpRewardTexture;
			if (rewardAmount != null) rewardAmount.Text = quest.XpReward.ToString();
		}
	}

	private void SetupClaimStatus(ItemList itemList, Quest quest)
	{
		var statusSprite = itemList.GetNode<Sprite2D>("CanClaimStatus");
		if (statusSprite != null)
		{
			bool canClaim = QuestManager.CanCompleteQuest(quest.Id);
			statusSprite.Texture = canClaim ? CanClaimTexture : CantClaimTexture;
		}
	}

	private void OnQuestButtonPressed(int questId)
	{
		if (QuestManager.CompleteQuest(questId))
		{
			GD.Print($"QuestsMenu: Quest {questId} completed successfully!");
			RefreshQuestList(); // Refresh the UI after completing quest
		}
		else
		{
			GD.Print($"QuestsMenu: Failed to complete quest {questId}");
		}
	}

	private class QuestItemUI
	{
		public ItemList ItemList { get; set; }
		public Quest Quest { get; set; }
	}
}
