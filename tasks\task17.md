1. Read player controller to see on which layer does player detector use to detect player. Then look at QuestBoard scene - it has Area2D which you need to use to detect when player is in range. Then when player is in range, and clicks R button then you need to open QuestsMenu (it's int QuestBoard scene). Open it the same way as AnvilMenu is opened and closed. When close button in QuestsMenu is clicked then you need to close it like in anvil menu. Initially it should be hidden (like AnvilMenu).
2. QuestMenu - here player would be able to complete quests to get some rewards. So we need a static class with defined quests: Id (int), Header (string - will be translated, and should be like QUEST_[id]_HEADER), description (string - will be translated, and should be like QUEST_[id]_DESCRIPTION), goldReward (int), xpReward (int), requirements (list of resource type with amount). Quest menu has item list - i defined 2 templates - Dark and Light. So if we have 3 quests it should be dark-light-dark. How do you set up ui:
* in item list - header and description should be taken from static quest data
* we have Price1Icon, Price1, Price2Icon, Price2, Price3Icon, Price3 - they should be set accordingly to things player should provide for given quest. Hide not used.
* RewardAmount - amount of reward (only gold or xp can be set as reward). export 2 textures - for gold and xp and i will set them. RewardIcon is an icon of this reward.
* Button in item list - it should work as "provide items" - if can afford, then remove items from player inventory, give rewards and refresh ui. If cant afford then dont complete. Also, there is sprite CanClaimStatus - export 2 sprites - canclaim and cantclaim - if can claim (player has enough items) then set sprite to canclaim, if not then set to cantclaim. Refresh every time player completes quests or opens quest panel.
Use animation player to open/close quests panel. 
3. In global data save system - create model for quests. In there there should be 2 lists: of quests completed and quests that 