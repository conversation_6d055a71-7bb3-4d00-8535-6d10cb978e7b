[gd_scene load_steps=6 format=3 uid="uid://kjpoq0o6wb7"]

[ext_resource type="Texture2D" uid="uid://ycvtjs7n05n8" path="res://resources/solaria/exterior/terrains/terrain_up_region_2.png" id="1_55od2"]
[ext_resource type="Texture2D" uid="uid://0xulpjecpn6w" path="res://resources/solaria/buildings/QuestsBoard2.png" id="2_q7yg7"]
[ext_resource type="PackedScene" uid="uid://bcswpp68n3vmf" path="res://scenes/UI/buildingMenus/QuestsMenu.tscn" id="3_q7yg7"]

[sub_resource type="CircleShape2D" id="CircleShape2D_55od2"]
radius = 17.0

[sub_resource type="RectangleShape2D" id="RectangleShape2D_55od2"]
size = Vector2(22, 7)

[node name="QuestBoard" type="Node2D"]
y_sort_enabled = true

[node name="Bg" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -42)
texture = ExtResource("1_55od2")
offset = Vector2(0, 42)

[node name="Board" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -7)
texture = ExtResource("2_q7yg7")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(0, -8)
shape = SubResource("CircleShape2D_55od2")

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(-20, 39, -19, 30, -31, 30, -32, -29, -26, -34, 26, -34, 32, -28, 30, 29, 21, 29, 21, 39, 31, 39, 35, 34, 35, -28, 27, -35, -26, -35, -35, -28, -35, 35, -30, 38, -25, 40)

[node name="StaticBody2D2" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D2"]
position = Vector2(0, 0.5)
shape = SubResource("RectangleShape2D_55od2")

[node name="QuestsMenu" parent="." instance=ExtResource("3_q7yg7")]
